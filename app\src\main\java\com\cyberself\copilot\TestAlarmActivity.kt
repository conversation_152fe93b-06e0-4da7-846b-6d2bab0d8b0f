package com.cyberself.copilot

import android.app.Activity
import android.app.AlarmManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.widget.Button
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast

class TestAlarmActivity : Activity() {
    private val TAG = "TestAlarmActivity"
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Create simple UI
        val layout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(50, 50, 50, 50)
        }
        
        val title = TextView(this).apply {
            text = "Alarm Test App"
            textSize = 24f
            setPadding(0, 0, 0, 30)
        }
        
        val button1 = Button(this).apply {
            text = "Set Alarm (30 seconds)"
            setOnClickListener { setTestAlarm(30) }
        }
        
        val button2 = Button(this).apply {
            text = "Set Alarm (2 minutes)"
            setOnClickListener { setTestAlarm(120) }
        }
        
        val button3 = Button(this).apply {
            text = "Check Permissions"
            setOnClickListener { checkPermissions() }
        }
        
        layout.addView(title)
        layout.addView(button1)
        layout.addView(button2)
        layout.addView(button3)
        
        setContentView(layout)
        
        Log.d(TAG, "TestAlarmActivity created")
    }
    
    private fun setTestAlarm(seconds: Int) {
        try {
            val alarmManager = getSystemService(Context.ALARM_SERVICE) as AlarmManager
            val alarmTime = System.currentTimeMillis() + (seconds * 1000)
            
            val testData = """{"message":"Test alarm triggered!","time":"${System.currentTimeMillis()}"}"""
            
            val intent = Intent(this, AlarmReceiver::class.java).apply {
                action = "com.cyberself.copilot.ALARM_TRIGGER"
                putExtra("ALARM_DATA", testData)
            }
            
            val requestCode = alarmTime.hashCode()
            val pendingIntent = PendingIntent.getBroadcast(
                this,
                requestCode,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (alarmManager.canScheduleExactAlarms()) {
                    alarmManager.setExactAndAllowWhileIdle(
                        AlarmManager.RTC_WAKEUP,
                        alarmTime,
                        pendingIntent
                    )
                    Toast.makeText(this, "Alarm set for $seconds seconds", Toast.LENGTH_SHORT).show()
                    Log.d(TAG, "Alarm scheduled for $seconds seconds from now")
                } else {
                    Toast.makeText(this, "Cannot schedule exact alarms - permission needed", Toast.LENGTH_LONG).show()
                }
            } else {
                alarmManager.setExactAndAllowWhileIdle(
                    AlarmManager.RTC_WAKEUP,
                    alarmTime,
                    pendingIntent
                )
                Toast.makeText(this, "Alarm set for $seconds seconds", Toast.LENGTH_SHORT).show()
                Log.d(TAG, "Alarm scheduled for $seconds seconds from now")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to set alarm: ${e.message}", e)
            Toast.makeText(this, "Failed to set alarm: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }
    
    private fun checkPermissions() {
        val alarmManager = getSystemService(Context.ALARM_SERVICE) as AlarmManager
        val canScheduleExactAlarms = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            alarmManager.canScheduleExactAlarms()
        } else {
            true
        }
        
        val message = "Can schedule exact alarms: $canScheduleExactAlarms"
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
        Log.d(TAG, message)
    }
}
